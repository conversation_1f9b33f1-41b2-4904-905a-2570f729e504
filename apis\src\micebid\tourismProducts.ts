import { download, get, post } from '../request'
import { 
    ITourismProductsFilter, 
    ITourismProducts,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const tourismProductsApi = {
    list: (params: ITourismProductsFilter): Promise<IPageResponse<ITourismProducts>> => {
        return get('/mice-bid/api/tour-product/page', params)
    },

    get: (id: number): Promise<ITourismProducts> => {
        return get('/mice-bid/api/tour-product/detail', {
            id
        })
    },

    save: (params: ITourismProducts): Promise<Result> => {
        return post('/mice-bid/api/tour-product/add', params)
    },

    edit: (params: ITourismProducts): Promise<Result> => {
        return post('/mice-bid/api/tour-product/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('/mice-bid/api/tour-product/delete', { id })
    },
}
