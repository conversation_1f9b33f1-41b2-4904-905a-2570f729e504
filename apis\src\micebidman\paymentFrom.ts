import { download, get, post } from '../request'
import {
    IPaymentFromFilter,
    IPaymentFrom,
    ICreatePaymentFromParams,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const paymentFromApi = {
    // 查询服务商所有的结算单信息
    getBillList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/payment/getBillList', params)
    },
    // 收款单列表查询
    getPage: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/payment/getPage', params)
    },
    getDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/payment/getDetails', {
            id
        })
    },
    // 创建付款单
    create: (params: ICreatePaymentFromParams): Promise<Result> => {
        return post('/mice-bid/api/mice/payment/create', params)
    },
    // 财务确认付款并上传付款凭证
    confirmPayment: (params: IPaymentFrom): Promise<Result> => {
        return post('/mice-bid/api/mice/payment/confirmPayment', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/paymentFrom/delete', { id })
    },
}
