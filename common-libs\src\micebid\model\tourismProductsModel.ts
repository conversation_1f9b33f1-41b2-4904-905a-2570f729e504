import { IPageRequest } from "../../basic";

export class ITourismProductsFilter extends IPageRequest {
    begin?:string
    end?:string
    //创建人姓名		
    createName?:string
    //创建时间			
    gmtCreate?:string
    //标题	
    title?:string
    //出发城市	
    startCity?:string
}


export class ITourismProducts {
    id?: number | null
    creator?:string
    //标题	
    title?:string
    //产品简介	
    briefIntroduction?:string
    //市场价			
    marketPrice?:string
    //员工价			
    staffPrice?:string
    //联系人工号		
    username?:string
    //联系人姓名	
    nickname?:string
    //出发城市	
    startCity?:string
    //外部链接	
    externalLinks?:string
    //详情	
    detail?:string
    //标签	
    tag?:string
    //生效开始时间		
    startTime?:string
    //生效结束时间		
    endTime?:string
    //创建人工号		
    createBy?:string
    //创建人姓名		
    createName?:string
    //创建时间			
    gmtCreate?:string
    //最后修改人工号			
    lastModifiedBy?:string
    //最后修改人姓名			
    lastModifiedName?:string
    //最后修改时间			
    gmtModified?:string
    //图片id			
    attachmentId?:string
    //图片id			
    type?:string
    //图片id			
    path?:string
    createTime?: string
    updater?: string
    updateTime?: string
    effectiveTime?: string
}
