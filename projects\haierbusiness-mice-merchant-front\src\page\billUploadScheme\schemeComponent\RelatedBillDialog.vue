<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';

// 类型定义
interface RelatedBillItem {
  id: string;
  sequenceNumber: number;
  date: string;
  project: string;
  category: string;
  contractPrice: number;
  contractQuantity: number;
  billPrice: number;
  billQuantity: number;
  relatedBill: string;
}

interface RelatedBillDialogProps {
  visible: boolean;
  billType: 'invoice' | 'waterBill';
  billData: any;
  demandInfo?: any; // 需求信息，包含 stays 数据
}

interface RelatedBillDialogEmits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: any): void;
  (e: 'updateStaysInvoiceId', data: { invoiceTempId: string; selectedStayIds: string[] }): void;
}

const props = defineProps<RelatedBillDialogProps>();
const emit = defineEmits<RelatedBillDialogEmits>();

// 响应式数据
const isAddMode = ref(false); // 是否为添加模式
const selectedRowKeys = ref<string[]>([]);
const loading = ref(false);
const editableAttachmentAmount = ref<number>(0); // 可编辑的附件总金额
const addedBillIds = ref<string[]>([]); // 记录已添加到关联账单的原始数据ID

// 分页相关
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: number[]) => `共 ${total} 条记录，显示 ${range[0]}-${range[1]} 条`,
});

// 筛选条件
const filterForm = ref({
  hotelPlan: '',
  project: '住宿',
  planDate: '',
});

// 项目选项
const projectOptions = [
  { value: '住宿', label: '住宿' },
  { value: '餐饮', label: '餐饮' },
  { value: '会场', label: '会场' },
  { value: '交通', label: '交通' },
];

// 已关联账单数据（初始为空，从后端加载）
const relatedBillList = ref<RelatedBillItem[]>([]);

// 可选择的账单数据（从 demandInfo.stays 生成）
const availableBillList = ref<RelatedBillItem[]>([]);

// 计算属性
const attachmentTotalAmount = computed(() => {
  return relatedBillList.value.reduce((sum, item) => sum + item.contractPrice * item.contractQuantity, 0);
});

const billTotalAmount = computed(() => {
  return relatedBillList.value.reduce((sum, item) => sum + item.billPrice * item.billQuantity, 0);
});

const modalTitle = computed(() => {
  if (isAddMode.value) {
    return '添加关联账单';
  }
  return `关联账单详情 - ${props.billType === 'invoice' ? '发票' : '水单'}`;
});

// 表格列定义
const viewColumns = [
  { title: '序号', dataIndex: 'sequenceNumber', width: 60 },
  { title: '日期', dataIndex: 'date', width: 100 },
  { title: '项目', dataIndex: 'project', width: 80 },
  { title: '类别', dataIndex: 'category', width: 80 },
  { title: '委约单价金额(人民币)', dataIndex: 'contractPrice', width: 150 },
  { title: '委约数量', dataIndex: 'contractQuantity', width: 80 },
  { title: '账单单价金额(人民币)', dataIndex: 'billPrice', width: 150 },
  { title: '账单实际数量', dataIndex: 'billQuantity', width: 120 },
  { title: '关联账单', dataIndex: 'relatedBill', width: 100 },
];

const addColumns = [
  { title: '序号', dataIndex: 'sequenceNumber', width: 60 },
  { title: '日期', dataIndex: 'date', width: 100 },
  { title: '项目', dataIndex: 'project', width: 80 },
  { title: '类别', dataIndex: 'category', width: 80 },
  { title: '委约单价金额', dataIndex: 'contractPrice', width: 120 },
  { title: '委约数量', dataIndex: 'contractQuantity', width: 80 },
  { title: '账单单价金额', dataIndex: 'billPrice', width: 120 },
  { title: '账单实际数量', dataIndex: 'billQuantity', width: 120 },
  { title: '状态', dataIndex: 'status', width: 80 },
];

// 筛选后的数据
const filteredAvailableBillList = computed(() => {
  let list = availableBillList.value;

  if (filterForm.value.project) {
    list = list.filter((item) => item.project === filterForm.value.project);
  }

  if (filterForm.value.hotelPlan) {
    list = list.filter(
      (item) => item.project.includes(filterForm.value.hotelPlan) || item.category.includes(filterForm.value.hotelPlan),
    );
  }

  if (filterForm.value.planDate) {
    list = list.filter((item) => item.date === filterForm.value.planDate);
  }

  // 更新分页总数
  pagination.value.total = list.length;

  return list;
});

// 计算是否全选（基于当前页面）
const isAllSelected = computed(() => {
  const currentPageData = getCurrentPageData();
  return currentPageData.length > 0 && currentPageData.every((item) => selectedRowKeys.value.includes(item.id));
});

// 获取当前页面数据
const getCurrentPageData = () => {
  const start = (pagination.value.current - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredAvailableBillList.value.slice(start, end);
};

// 从 demandInfo.stays 生成可选择的账单数据
const generateAvailableBillList = () => {
  if (!props.demandInfo?.stays) {
    return;
  }

  const billList: RelatedBillItem[] = props.demandInfo.stays.map((stay: any, index: number) => ({
    id: `stay_${stay.id || index}`,
    sequenceNumber: index + 1,
    date: stay.demandDate || '',
    project: '住宿',
    category: '原需求',
    contractPrice: stay.schemeUnitPrice || 0,
    contractQuantity: stay.schemeRoomNum || 0,
    billPrice: stay.schemeUnitPrice || 0, // 初始值，可以后续修改
    billQuantity: stay.schemeRoomNum || 0, // 初始值，可以后续修改
    relatedBill: '',
  }));

  availableBillList.value = billList;
};

// 方法
const handleCancel = () => {
  emit('update:visible', false);
  isAddMode.value = false;
  selectedRowKeys.value = [];
  resetFilter();
};

const handleAddRelatedBill = () => {
  isAddMode.value = true;
  // 自动选中已添加过的数据
  selectedRowKeys.value = [...addedBillIds.value];
};

const handleBackToView = () => {
  isAddMode.value = false;
  selectedRowKeys.value = [];
  resetFilter();
};

const handleConfirmAdd = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  const selectedItems = filteredAvailableBillList.value.filter((item) => selectedRowKeys.value.includes(item.id));

  // 只添加新选中的数据（排除已存在的）
  const newSelectedItems = selectedItems.filter((item) => !addedBillIds.value.includes(item.id));

  // 添加到关联账单列表
  const newItems = newSelectedItems.map((item) => ({
    ...item,
    id: `new_${Date.now()}_${item.id}`,
    sequenceNumber: relatedBillList.value.length + newSelectedItems.indexOf(item) + 1,
    relatedBill: '查看>>',
  }));

  relatedBillList.value.push(...newItems);

  // 记录所有选中的原始数据ID（包括新添加和已存在的）
  addedBillIds.value = [...new Set([...addedBillIds.value, ...selectedRowKeys.value])];

  // 如果是发票类型，需要将发票的tempId关联到选中的stays记录
  if (props.billType === 'invoice' && props.billData?.tempId) {
    const selectedStayIds = selectedRowKeys.value.filter(id => id.startsWith('stay_'));
    if (selectedStayIds.length > 0) {
      emit('updateStaysInvoiceId', {
        invoiceTempId: props.billData.tempId,
        selectedStayIds: selectedStayIds
      });
    }
  }

  message.success(`成功添加 ${newSelectedItems.length} 条关联账单`);

  // 返回查看模式
  handleBackToView();
};

const resetFilter = () => {
  filterForm.value = {
    hotelPlan: '',
    project: '住宿',
    planDate: '',
  };
};

const toggleSelectAll = () => {
  const currentPageData = getCurrentPageData();
  const currentPageIds = currentPageData.map((item) => item.id);

  if (isAllSelected.value) {
    // 取消当前页所有选择
    selectedRowKeys.value = selectedRowKeys.value.filter((id) => !currentPageIds.includes(id));
  } else {
    // 选中当前页所有项（保留其他页的选择）
    const newSelectedKeys = [...new Set([...selectedRowKeys.value, ...currentPageIds])];
    selectedRowKeys.value = newSelectedKeys;
  }
};

const handleSearch = () => {
  // 查询逻辑，这里可以添加搜索过滤功能
  console.log('执行查询:', filterForm.value);
  // 重置分页到第一页
  pagination.value.current = 1;
  // 保持选择状态，不清空
  message.success('查询完成');
};

const handleSelectChange = (selectedKeys: string[]) => {
  selectedRowKeys.value = selectedKeys;
};

// 分页变化处理
const handleTableChange = (pag: any) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  // 不清空选择状态，保持跨页选择
};

// 格式化金额显示
const formatAmount = (amount: number) => {
  return `${amount}元`;
};

// 监听attachmentTotalAmount变化，同步到可编辑金额
watch(
  attachmentTotalAmount,
  (newValue) => {
    editableAttachmentAmount.value = newValue;
  },
  { immediate: true },
);

// 监听 demandInfo 变化
watch(
  () => props.demandInfo,
  (newValue) => {
    if (newValue?.stays) {
      // 打印第一条 stays 数据的详细信息
      if (newValue.stays.length > 0) {
      }
      // 生成可选择的账单数据
      generateAvailableBillList();
    }
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <a-modal
    :open="visible"
    :title="modalTitle"
    width="1200px"
    :footer="null"
    :body-style="{ height: '400px', overflow: 'auto' }"
    @cancel="handleCancel"
  >
    <!-- 查看模式 -->
    <div v-if="!isAddMode" class="view-mode">
      <!-- 金额统计 -->
      <div class="amount-summary">
        <span class="amount-item">
          附件总金额:
          <input
            v-model.number="editableAttachmentAmount"
            class="amount-input"
            type="number"
            placeholder="请输入金额"
          />元
        </span>
        <span class="amount-item">
          账单合计金额: <span class="amount-value red">{{ formatAmount(billTotalAmount) }}</span>
        </span>
      </div>

      <!-- 关联账单表格 -->
      <div class="table-wrapper">
        <a-table
          :columns="viewColumns"
          :data-source="relatedBillList"
          :pagination="false"
          size="small"
          :scroll="{ x: 900 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractPrice'">
              {{ formatAmount(record.contractPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'billPrice'">
              {{ formatAmount(record.billPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'relatedBill'">
              <a-button type="link" size="small">
                {{ record.relatedBill }}
              </a-button>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 添加按钮 -->
      <div class="add-button-wrapper">
        <a-button type="link" @click="handleAddRelatedBill"> + 增加账单关联 </a-button>
      </div>
    </div>

    <!-- 添加模式 -->
    <div v-else class="add-mode">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>方案酒店：</label>
          </a-col>
          <a-col :span="4">
            <a-input v-model:value="filterForm.hotelPlan" placeholder="请输入" allow-clear />
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>项目：</label>
          </a-col>
          <a-col :span="3">
            <a-select
              v-model:value="filterForm.project"
              :options="projectOptions"
              placeholder="请选择项目"
              allow-clear
              style="width: 100%"
            />
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>方案日期：</label>
          </a-col>
          <a-col :span="4">
            <a-date-picker
              v-model:value="filterForm.planDate"
              placeholder="请选择日期"
              allow-clear
              style="width: 100%"
            />
          </a-col>
          <a-col :span="7" style="text-align: left; padding-left: 10px">
            <a-button style="margin-right: 10px" @click="resetFilter">重置</a-button>
            <a-button type="primary" @click="handleSearch">查询</a-button>
          </a-col>
        </a-row>
        <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <a-col :span="24" style="text-align: left">
            <a-button @click="toggleSelectAll">{{ isAllSelected ? '取消全选' : '全选' }}</a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 可选择账单表格 -->
      <div class="table-wrapper">
        <a-table
          :columns="addColumns"
          :data-source="filteredAvailableBillList"
          :pagination="pagination"
          size="small"
          :scroll="{ x: 900, y: 300 }"
          row-key="id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: handleSelectChange,
            type: 'checkbox',
          }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractPrice'">
              {{ formatAmount(record.contractPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'billPrice'">
              {{ formatAmount(record.billPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'status'">
              <a-tag v-if="addedBillIds.includes(record.id)" color="green">已添加</a-tag>
              <a-tag v-else color="blue">可添加</a-tag>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button @click="handleBackToView"> 返回 </a-button>
        <a-button type="primary" @click="handleConfirmAdd"> 确认添加 </a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.view-mode {
  .amount-summary {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 4px;
    display: flex;
    gap: 32px;

    .amount-item {
      font-size: 14px;
      color: #333;

      .amount-value {
        font-weight: 600;
        margin-left: 4px;

        &.red {
          color: #ff4d4f;
        }
      }

      .amount-input {
        border: none;
        border-bottom: 1px solid #d9d9d9;
        background: transparent;
        outline: none;
        font-weight: 600;
        margin: 0 4px;
        padding: 2px 4px;
        min-width: 80px;
        text-align: center;

        &:focus {
          border-bottom: 2px solid #1890ff;
        }

        &::placeholder {
          color: #bfbfbf;
          font-weight: normal;
        }
      }
    }
  }

  .add-button-wrapper {
    margin-top: 16px;
    text-align: center;
    padding: 12px;
    border-top: 1px solid #d9d9d9;
    background-color: #fafafa;
  }
}

.add-mode {
  .filter-section {
    margin-bottom: 16px;
    border-radius: 4px;
  }

  .action-buttons {
    margin-top: 16px;
    text-align: center;
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}

.table-wrapper {
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 500;
      text-align: center;
    }

    .ant-table-tbody > tr > td {
      text-align: center;
    }
  }
}
</style>
