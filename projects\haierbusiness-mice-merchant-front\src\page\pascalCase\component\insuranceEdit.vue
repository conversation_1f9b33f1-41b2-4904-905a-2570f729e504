<template>
  <div class="insurance-edit-page">
    <h-card class="form-card">
      <h-form ref="formRef" :model="formData" :rules="rules" layout="horizontal" :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }" @finish="handleSubmit" hide-required-mark>
        <h-divider class="section-divider">{{ isEdit ? '保险编辑' : '保险新增' }}</h-divider>
        <!-- 产品名称 -->
        <h-form-item label="产品名称：" name="insuranceName">
          <h-input v-model:value="formData.insuranceName" placeholder="请输入产品名称" :disabled="isViewMode" />
        </h-form-item>

        <!-- 保费 -->
        <h-form-item label="保费：" name="premium">
          <h-input v-model:value="formData.premium" placeholder="请输入保费金额" type="number" :min="0" :step="0.01"
            :disabled="isViewMode" addon-after="元/人/天">
          </h-input>
        </h-form-item>

        <!-- 年龄要求 -->
        <h-form-item label="年龄要求：" name="ageRequirement">
          <h-input v-model:value="formData.ageRequirement" placeholder="请输入年龄区间,(例如18周岁-60周岁)" :disabled="isViewMode" />
        </h-form-item>

        <!-- 等待期 -->
        <h-form-item label="等待期：" name="waitingPeriod">
          <h-input v-model:value="formData.waitingPeriod" placeholder="请输入" :disabled="isViewMode" />
        </h-form-item>

        <!-- 产品特色 -->
        <h-form-item label="产品特色：" name="productFeatures">
          <h-upload v-model:fileList="featureFileList" :custom-request="uploadRequest" :multiple="false" :max-count="1"
            :disabled="isViewMode" :show-upload-list="{ showRemoveIcon: !isViewMode }" @remove="handleRemoveFeature"
            :before-upload="beforeUploadFeature" accept=".jpg, .png, .jpeg" list-type="picture-card"
            @change="handleFileChange" @preview="handlePreview">
            <div v-if="featureFileList.length < 1 && !isViewMode">
              <PlusOutlined class="upload-icon-large" />
            </div>
          </h-upload>
          <div style="color: #999; font-size: 12px; margin-top: 4px;">
            请上传尺寸为400*400，大小不超过2MB，格式为PNG/JPG/JPEG的图片
          </div>
        </h-form-item>

        <!-- 投保须知 -->
        <h-form-item label="投保须知：" name="insuranceNotice">
          <h-textarea v-model:value="formData.insuranceNotice" placeholder="非必填,请输入投保须知" :rows="4"
            :disabled="isViewMode" />
        </h-form-item>

        <!-- 理赔流程 -->
        <h-form-item label="理赔流程：" name="claimProcess">
          <h-textarea v-model:value="formData.claimProcess" placeholder="非必填,请输入理赔流程" :rows="4"
            :disabled="isViewMode" />
        </h-form-item>

        <!-- 温馨提示 -->
        <h-form-item label="温馨提示：" name="warmTips">
          <h-textarea v-model:value="formData.warmTips" placeholder="非必填,请输入温馨提示" :rows="4" :disabled="isViewMode" />
        </h-form-item>

        <!-- 常见问题 -->
        <h-form-item label="常见问题：" name="faq">
          <h-textarea v-model:value="formData.faq" placeholder="非必填,请输入常见问题" :rows="4" :disabled="isViewMode" />
        </h-form-item>

        <!-- 免除责任条款 -->
        <!-- <h-form-item label="免除责任条款：" name="exemptionTerms">
          <h-upload
            v-model:fileList="exemptionFileList"
            :custom-request="(options) => uploadRequest({ ...options, data: { type: 'exemption' } })"
            :max-count="1"
            :before-upload="beforeUploadPdf"
            accept=".pdf"
            :disabled="isViewMode"
            :show-upload-list="{ showRemoveIcon: !isViewMode }"
            list-type="text"
            @remove="handleRemoveExemption"
          >
            <h-button :loading="uploadLoading" :disabled="isViewMode"> <UploadOutlined /> 上传文件 </h-button>
          </h-upload>
        </h-form-item> -->

        <!-- 产品条款 -->
        <h-form-item label="产品条款：" name="productTerms">
          <h-upload v-model:fileList="termsFileList"
            :custom-request="(options) => uploadRequest({ ...options, data: { type: 'terms' } })" :multiple="true"
            :before-upload="beforeUploadPdf" accept=".pdf" :disabled="isViewMode"
            :show-upload-list="{ showRemoveIcon: !isViewMode }" list-type="text" @remove="handleRemoveTerms"
            @change="handleChangePdf" auto-upload="false">
            <h-button :loading="uploadLoading" :disabled="isViewMode" @click="handleUploadClick">
              <UploadOutlined /> 上传PDF文件
            </h-button>
          </h-upload>
          <div style="color: #999; font-size: 12px; margin-top: 4px;">
            支持上传多个PDF格式文件，单个文件大小不超过2MB
          </div>
        </h-form-item>
      </h-form>
    </h-card>

    <!-- 固定底部按钮 -->
    <div class="footer-container">
      <template v-if="isViewMode">
        <h-button @click="goBack"> 返回 </h-button>
      </template>
      <template v-else>
        <h-button @click="goBack" style="margin-right: 10px"> 取消 </h-button>
        <h-button type="primary" class="submit-button" @click="handleSubmit" :loading="submitLoading || uploadLoading">
          保存
        </h-button>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, inject } from 'vue';
import {
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Textarea as hTextarea,
  Button as hButton,
  Upload as hUpload,
  Card as hCard,
  Divider as hDivider,
  message,
  Upload,
} from 'ant-design-vue';
import { PlusOutlined, CloseOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { fileApi, pascalCaseApi } from '@haierbusiness-front/apis';
import { useRouter, useRoute } from 'vue-router';
import { FileTypeConstant } from '@haierbusiness-front/common-libs';
import {
  IPascalCase,
  IInsuranceDescription,
  IInsuranceDescriptionItem,
  InsuranceFormData,
} from '@haierbusiness-front/common-libs';
import { getFileNameFromPath } from '@haierbusiness-front/utils';
import type { Ref } from 'vue';

// 路由实例
const router = useRouter();
const route = useRoute();

const currentRouter = ref();
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

// 判断是否为编辑模式
const isEdit = ref(false);
const isViewMode = ref(false);
const insuranceId = ref<string | null>(null);

// 表单引用
const formRef = ref();

// 文件列表管理
const featureFileList = ref<any[]>([]);
const termsFileList = ref<any[]>([]);
const exemptionFileList = ref<any[]>([]); // 保留变量声明但功能已注释

// 删除记录管理 - 记录被删除的 caId，用于重新上传时复用
const deletedTermsRecords = ref<Array<{ caId: number; type: number }>>([]);
const deletedFeatureRecords = ref<Array<{ caId: number; type: number }>>([]);

// 表单数据
const formData = reactive<InsuranceFormData>({
  insuranceName: '',
  premium: '',
  ageRequirement: '',
  waitingPeriod: '',
  productFeatures: [],
  insuranceNotice: '',
  claimProcess: '',
  warmTips: '',
  faq: '',
  exemptionTerms: '', // 已注释UI功能，但保留字段以兼容类型定义
  productTerms: '', // 保持原类型，内部处理为数组
});

// 上传状态
const uploadLoading = ref<boolean>(false);
const submitLoading = ref<boolean>(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 产品特色图片预览
const featureImageUrl = ref<string>('');

// 表单验证规则
const rules: Record<string, any[]> = {
  insuranceName: [{ required: true, message: '请输入产品名称', trigger: ['blur', 'input'] }],
  premium: [
    { required: true, message: '请输入保费', trigger: ['blur', 'input'] },
    {
      pattern: /^\d+(\.\d{1,2})?$/,
      message: '请输入有效的金额，最多保留两位小数',
      trigger: ['blur', 'input'],
    },
  ],
  ageRequirement: [
    { required: true, message: '请输入年龄要求', trigger: ['blur', 'input'] },
    { pattern: /^[^-].*$/, message: '年龄要求不能为负数', trigger: ['blur', 'input'] },
  ],
  waitingPeriod: [
    { required: true, message: '请输入等待期', trigger: ['blur', 'input'] },
    { pattern: /^[^-].*$/, message: '等待期不能为负数', trigger: ['blur', 'input'] },
  ],
};

// 解析description字段的辅助函数
const parseDescriptionField = (
  description: string | IInsuranceDescription | IInsuranceDescriptionItem[],
): IInsuranceDescription => {
  try {
    if (Array.isArray(description)) {
      // 如果是数组格式，解析数组中的键值对
      const result: IInsuranceDescription = {};
      const descArray = description as IInsuranceDescriptionItem[];
      descArray.forEach((item) => {
        switch (item.key) {
          case 'ageRequirement':
            result.ageRequirement = item.value;
            break;
          case 'waitingPeriod':
            result.waitingPeriod = item.value;
            break;
          case 'insuranceNotice':
            result.insuranceNotice = item.value;
            break;
          case 'claimProcess':
            result.claimProcess = item.value;
            break;
          case 'warmTips':
            result.warmTips = item.value;
            break;
          case 'faq':
            result.faq = item.value;
            break;
          // case 'exemptionTerms':
          //   result.exemptionTerms = item.value;
          //   break;
        }
      });
      return result;
    } else if (typeof description === 'object') {
      // 如果是对象，直接返回
      return description as IInsuranceDescription;
    } else if (typeof description === 'string') {
      // 如果是字符串，尝试解析为JSON
      const parsed = JSON.parse(description);
      if (Array.isArray(parsed)) {
        // 解析为数组
        const result: IInsuranceDescription = {};
        parsed.forEach((item) => {
          if (item.key && item.value) {
            switch (item.key) {
              case 'ageRequirement':
                result.ageRequirement = item.value;
                break;
              case 'waitingPeriod':
                result.waitingPeriod = item.value;
                break;
              case 'insuranceNotice':
                result.insuranceNotice = item.value;
                break;
              case 'claimProcess':
                result.claimProcess = item.value;
                break;
              case 'warmTips':
                result.warmTips = item.value;
                break;
              case 'faq':
                result.faq = item.value;
                break;
              // case 'exemptionTerms':
              //   result.exemptionTerms = item.value;
              //   break;
            }
          }
        });
        return result;
      } else if (typeof parsed === 'object') {
        // 解析为对象
        return parsed as IInsuranceDescription;
      }
    }
  } catch (e) {
    // 如果解析失败，说明是纯文本，放到投保须知中
    console.warn('description解析失败，使用字符串格式:', e);
    return { insuranceNotice: description as string };
  }
  return {};
};

// 页面初始化
onMounted(() => {
  currentRouter.value = router;
  // 根据路由查询参数判断是新增还是编辑
  if (route.query.id) {
    isEdit.value = true;
    insuranceId.value = route.query.id as string;
    // TODO: 根据ID获取保险详情数据
    loadInsuranceData(insuranceId.value);
  }
  // 新增：判断是否为查看模式
  if (route.query.mode === 'view') {
    isViewMode.value = true;
  }
});

// 加载保险数据（编辑模式）
const loadInsuranceData = async (id: string) => {
  try {
    console.log('加载保险数据:', id);
    const response = await pascalCaseApi.insuranceGet(parseInt(id));

    if (response) {
      // 处理API返回的数据，转换为表单数据格式
      formData.insuranceName = response.insuranceName || '';
      // 优先使用price字段，如果没有则使用marketPrice
      formData.premium = (response.price || response.marketPrice)?.toString() || '';

      // 优先使用新的独立字段，如果不存在则从description中解析（向后兼容）
      // 解析旧的description字段数据
      let descriptionData: IInsuranceDescription = {};
      if (response.description) {
        descriptionData = parseDescriptionField(response.description);
      }

      formData.ageRequirement = response.ageRequire || descriptionData.ageRequirement || '';
      formData.waitingPeriod = response.waitingPeriod || descriptionData.waitingPeriod || '';
      formData.insuranceNotice = response.notice || descriptionData.insuranceNotice || '';
      formData.claimProcess = response.claimProcess || descriptionData.claimProcess || '';
      formData.warmTips = response.hintMessage || descriptionData.warmTips || '';
      formData.faq = response.question || descriptionData.faq || '';

      // 处理 path 字段，兼容旧的 images 字段
      const imageSource = response.path || response.images;
      if (imageSource && Array.isArray(imageSource)) {
        imageSource.forEach((image) => {
          // 确保 image.path 存在
          if (!image.path) return;

          if (image.type.toString() === FileTypeConstant.PRODUCT_FEATURE.code.toString()) {
            // 产品特色图片 (type 26)
            formData.productFeatures = [image.path]; // 保存图片路径
            featureFileList.value = [
              {
                uid: image.caId || '-1', // 使用 caId 作为 uid
                caId: image.caId, // 保存 caId
                name: '产品特色.jpg',
                status: 'done',
                url: image.path, // 用于预览
                filePath: image.path, // 保存路径
              },
            ];
          } else if (image.type.toString() === FileTypeConstant.PRODUCT_CLAUSE.code.toString()) {
            // 为产品条款添加到文件列表
            termsFileList.value.push({
              uid: image.caId || `-2-${termsFileList.value.length}`, // 使用 caId 或其他唯一值
              caId: image.caId, // 保存 caId
              name: getFileNameFromPath(image.path) || '产品条款文件',
              status: 'done',
              url: image.path,
              filePath: image.path,
            });
            // } else if (image.type.toString() === FileTypeConstant.EXEMPTION_TERMS.code.toString()) {
            //   // 免除责任条款文件 (type 27)
            //   formData.exemptionTerms = image.path; // 保存文件路径
            //   exemptionFileList.value = [
            //     {
            //       uid: image.caId || '-3', // 使用 caId 或其他唯一值
            //       caId: image.caId, // 保存 caId
            //       name: image.path.split('/').pop() || '免除责任条款文件',
            //       status: 'done',
            //       url: image.path,
            //       filePath: image.path,
            //     },
            //   ];
          }
        });
      }
    }
  } catch (error) {
    console.error('加载保险数据失败:', error);
    message.error('加载保险数据失败');
  }
};

// 文件上传前验证
const beforeUpload = (file: File) => {
  const isValidType = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png',
    'image/jpg',
  ].includes(file.type);

  if (!isValidType) {
    message.error('请上传PDF、DOC、DOCX、JPG、PNG格式的文件!');
    return false;
  }

  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('文件大小不能超过 2MB!');
    return false;
  }

  return true;
};

// PDF文件上传前验证 - 严格验证PDF格式
const beforeUploadPdf = (file: File) => {
  console.log('文件类型检查:', file.type, '文件名:', file.name);

  // 检查MIME类型
  const isPdf = file.type === 'application/pdf';

  // 检查文件扩展名（双重验证）
  const fileName = file.name.toLowerCase();
  const hasValidExtension = fileName.endsWith('.pdf');

  // 立即阻止非PDF文件
  if (!isPdf || !hasValidExtension) {
    message.error('只能上传PDF格式的文件！请重新选择正确的PDF文件。');
    // 返回false会阻止文件被添加到文件列表中
    return false;
  }

  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('PDF文件大小不能超过 2MB！请选择更小的文件。');
    return false;
  }

  // 通过所有验证
  message.success('PDF文件验证通过，准备上传...');
  return true || Upload.LIST_IGNORE;
};

const handleChangePdf = ({ fileList }) => {
  termsFileList.value = fileList.filter(file => {
    const fileType = file.raw?.type || file.type;
    return file.status !== 'error' &&
      (!fileType || fileType === 'application/pdf');
  });
}


// 处理上传按钮点击事件 - 提前提示用户
const handleUploadClick = () => {
  // 在用户点击上传按钮时显示提示信息
};

// 产品特色图片上传前验证
const beforeUploadFeature = (file: File): boolean | Promise<boolean> => {
  // 检查文件格式
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
  if (!isJpgOrPng) {
    message.error('请上传PNG/JPG/JPEG格式的图片!');
    return false;
  }

  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过2MB!');
    return false;
  }

  // 检查图片尺寸
  return new Promise<boolean>((resolve, reject) => {
    const img = new Image();
    const objectUrl = URL.createObjectURL(file);
    img.onload = () => {
      const { naturalWidth: width, naturalHeight: height } = img;
      // if (width !== 400 || height !== 400) {
      //   URL.revokeObjectURL(objectUrl);
      //   message.error('请上传尺寸为400*400的图片!');
      //   resolve(false)
      // } else {
      //   message.success('图片验证通过，准备上传...');
      //   resolve(true);
      // }
      resolve(true);
    };
    img.onerror = () => {
      URL.revokeObjectURL(objectUrl);
      message.error('图片格式错误，无法读取图片尺寸!');
      resolve(false);
    };
    img.src = objectUrl;

  });
};

const handleFileChange = ({ fileList }) => {
  // 过滤掉校验失败的文件
  featureFileList.value = fileList.filter(file =>
    file.status === 'done' || file.status === 'uploading'
  );
  console.log(featureFileList.value,"featureFileList.value");
  
};


// 图片预览
const handlePreview = (file: any) => {
  if (file.url || file.filePath) {
    const previewUrl = file.url || file.filePath;
    const image = new Image();
    image.src = previewUrl || '';
    const newWindow = window.open('');
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>图片预览</title>
            <style>
              body {
                margin: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                background-color: #000;
              }
              img {
                max-width: 100%;
                max-height: 100%;
              }
            </style>
          </head>
          <body>
            <img src="${previewUrl}" />
          </body>
        </html>
      `);
    }
  }
};

// 上传请求处理 - 完全按照 edit-dialog.vue 的方式实现
const uploadRequest = (options: any) => {
  console.log('uploadRequest 被调用了:', options);

  uploadLoading.value = true;
  submitLoading.value = true;

  const uploadFormData = new FormData();
  uploadFormData.append('file', options.file);

  fileApi
    .upload(uploadFormData)
    .then((response) => {
      if (response && response.path) {
        const fullPath = baseUrl + response.path;

        // 按照 edit-dialog.vue 的方式设置文件属性
        options.file.filePath = fullPath;
        options.file.fileName = options.file.name;
        options.file.url = fullPath;
        options.file.thumbUrl = fullPath;

        if (options.data && options.data.type === 'terms') {
          // 产品条款上传 - 支持多个文件
          // 检查是否有删除记录，优先复用被删除的 caId
          let reuseRecord = null;
          if (deletedTermsRecords.value.length > 0) {
            reuseRecord = deletedTermsRecords.value.shift(); // 取出第一个删除记录
            console.log('复用删除的 caId:', reuseRecord);
          }

          // 如果有复用记录，使用其 caId；否则不设置 caId（新文件）
          if (reuseRecord) {
            options.file.caId = reuseRecord.caId;
            console.log('为新上传文件分配 caId:', reuseRecord.caId);
          }

          // 将当前文件路径添加到文件列表中，不直接修改formData.productTerms
          // 在提交时会处理多个文件
          // } else if (options.data && options.data.type === 'exemption') {
          //   // 免除责任条款上传
          //   formData.exemptionTerms = fullPath;
        } else {
          // 产品特色上传 - 更新图片预览
          featureImageUrl.value = fullPath;
          // 将图片路径添加到产品特色数组
          formData.productFeatures = [fullPath];

          // 检查是否有删除记录，优先复用被删除的 caId
          let reuseRecord = null;
          if (deletedFeatureRecords.value.length > 0) {
            reuseRecord = deletedFeatureRecords.value.shift(); // 取出第一个删除记录
            console.log('复用删除的产品特色 caId:', reuseRecord);
          }

          // 如果有复用记录，使用其 caId；否则不设置 caId（新文件）
          if (reuseRecord) {
            options.file.caId = reuseRecord.caId;
            console.log('为新上传的产品特色文件分配 caId:', reuseRecord.caId);
          }
        }

        options.onSuccess(response, options.file);
        message.success('文件上传成功');
      } else {
        options.onError('上传失败');
        message.error('上传失败，请重试');
      }
    })
    .catch((error) => {
      console.error('文件上传失败:', error);
      message.error('文件上传失败，请重试');
      options.onError && options.onError(error);
    })
    .finally(() => {
      uploadLoading.value = false;
      submitLoading.value = false;
    });
};

// 移除产品特色文件
const handleRemoveFeature = (file: any) => {
  const index = featureFileList.value.indexOf(file);
  if (index !== -1) {
    // 如果删除的文件有 caId（即从服务器加载的文件），记录到删除列表中
    if (file.caId) {
      deletedFeatureRecords.value.push({
        caId: file.caId,
        type: FileTypeConstant.PRODUCT_FEATURE.code // type 26
      });
      console.log('记录删除的产品特色文件:', { caId: file.caId, fileName: file.name });
    }

    // 从文件列表中移除
    featureFileList.value.splice(index, 1);
    // 清除图片预览
    featureImageUrl.value = '';
    // 清除产品特色数据
    formData.productFeatures = [];
    console.log('当前产品特色删除记录:', deletedFeatureRecords.value);
  }
};

// 移除产品条款文件
const handleRemoveTerms = (file: any) => {
  const index = termsFileList.value.indexOf(file);
  if (index !== -1) {
    // 如果删除的文件有 caId（即从服务器加载的文件），记录到删除列表中
    if (file.caId) {
      deletedTermsRecords.value.push({
        caId: file.caId,
        type: FileTypeConstant.PRODUCT_CLAUSE.code // type 24
      });
      console.log('记录删除的产品条款文件:', { caId: file.caId, fileName: file.name });
    }

    // 从文件列表中移除
    termsFileList.value.splice(index, 1);
    console.log('当前删除记录:', deletedTermsRecords.value);
  }
};

// 移除免除责任条款文件 - 已注释功能
// const handleRemoveExemption = (file: any) => {
//   const index = exemptionFileList.value.indexOf(file);
//   if (index !== -1) {
//     exemptionFileList.value.splice(index, 1);
//     formData.exemptionTerms = ''; // 同步清除表单数据
//   }
// };

// 移除富文本编辑器相关函数，现在使用普通文本框

// 返回上一页
const goBack = () => {
  if (isCloseLastTab) {
    // 关闭当前页签
    isCloseLastTab.value = true;
  }
  currentRouter.value.push({ path: '/mice-merchant/pascalCase/index' });
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    if(featureFileList.value.length == 0 || !featureFileList.value){
      message.error('请上传产品特色图片')
      return false
    }
    if(termsFileList.value.length == 0 || !termsFileList.value){
      message.error('请上传产品条款文件')
      return false
    }

    // 组织文件列表的基础数据
    const fileListForPayload: any[] = [];
    if (featureFileList.value.length > 0) {
      const featureImage = featureFileList.value[0];
      fileListForPayload.push({
        type: FileTypeConstant.PRODUCT_FEATURE.code.toString(),
        path: featureImage.url || featureImage.filePath,
        caId: featureImage.caId,
      });
    }

    // 处理多个产品条款文件
    if (termsFileList.value.length > 0) {
      termsFileList.value.forEach(termsFile => {
        fileListForPayload.push({
          type: FileTypeConstant.PRODUCT_CLAUSE.code.toString(),
          path: termsFile.url || termsFile.filePath,
          caId: termsFile.caId,
        });
      });
    }

    // 处理已删除但需要保留 caId 的文件（将 path 设置为空）
    if (deletedTermsRecords.value.length > 0) {
      deletedTermsRecords.value.forEach(record => {
        fileListForPayload.push({
          type: record.type.toString(),
          path: '', // 设置为空路径
          caId: record.caId,
        });
      });
      console.log('添加已删除的产品条款文件记录（path为空）:', deletedTermsRecords.value);
    }

    // 处理已删除的产品特色文件记录
    if (deletedFeatureRecords.value.length > 0) {
      deletedFeatureRecords.value.forEach(record => {
        fileListForPayload.push({
          type: record.type.toString(),
          path: '', // 设置为空路径
          caId: record.caId,
        });
      });
      console.log('添加已删除的产品特色文件记录（path为空）:', deletedFeatureRecords.value);
    }
    // 免除责任条款功能已注释
    // if (exemptionFileList.value.length > 0) {
    //   const exemptionFile = exemptionFileList.value[0];
    //   fileListForPayload.push({
    //     type: FileTypeConstant.EXEMPTION_TERMS.code.toString(),
    //     path: exemptionFile.url || exemptionFile.filePath,
    //     caId: exemptionFile.caId,
    //   });
    // }

    // 组织最终提交数据 - 使用独立字段
    const submitData: IPascalCase = {
      insuranceName: formData.insuranceName,
      price: parseFloat(formData.premium) || 0,
      ageRequire: formData.ageRequirement,
      waitingPeriod: formData.waitingPeriod,
      notice: formData.insuranceNotice,
      claimProcess: formData.claimProcess,
      hintMessage: formData.warmTips,
      question: formData.faq,
    };

    if (isEdit.value) {
      // 编辑模式
      submitData.id = parseInt(insuranceId.value!);
      submitData.pathList = fileListForPayload.map((file) => ({
        ...file,
        type: Number(file.type),
      }));
    } else {
      // 新增模式
      submitData.images = fileListForPayload.map((file) => ({
        ...file,
        type: file.type.toString(),
      }));
    }

    console.log('提交保险数据:', submitData);
    submitLoading.value = true;

    if (isEdit.value) {
      await pascalCaseApi.insuranceEdit(submitData);
      message.success('保险信息更新成功！');
    } else {
      await pascalCaseApi.insuranceSave(submitData);
      message.success('保险创建成功！');
    }

    // 操作成功后返回列表页
    if (isCloseLastTab) isCloseLastTab.value = true; // 关闭当前页签
    currentRouter.value.push({ path: '/mice-merchant/pascalCase/index' });
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  } finally {
    submitLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.insurance-edit-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  position: relative;
}

.form-card {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  margin-bottom: 60px;

  :deep(.ant-card-body) {
    padding: 20px;
  }
}

.upload-icon-large {
  font-size: 24px;
}

/* 设置输入框的宽度 */
:deep(.ant-form-item-control-input) {
  max-width: 360px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-divider) {
  margin: 16px 0;
  font-weight: 500;

  /* 让分割线文字靠左 */
  &.ant-divider-with-text-center {
    &::before {
      width: 0%;
    }

    &::after {
      width: 100%;
    }

    .ant-divider-inner-text {
      padding-left: 0;
    }
  }
}

:deep(.ant-form-item-label) {
  font-weight: normal;
}

:deep(.ant-input),
:deep(.ant-input-textarea textarea) {
  border-radius: 4px;
}

:deep(.ant-upload.ant-upload-select) {
  display: inline-block;
}

.cancel-button {
  margin-right: 8px;
}

/* 固定底部按钮样式 */
.footer-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.submit-button {
  height: 32px;
  border-radius: 4px;
}

/* 隐藏必填标记的红色星号 */
:deep(.ant-form-item-required::before) {
  display: none !important;
}

/* 移除卡片边框 */
:where(.css-dev-only-do-not-override-1cqaw7h).ant-card-bordered {
  border: none;
}

/* 分割线样式 */
:where(.css-dev-only-do-not-override-1cqaw7h).ant-divider-horizontal.ant-divider-with-text::after {
  border-block-start: 2px solid #f0f0f0;
  transform: translateY(8%);

  :deep(.ant-divider-inner-text) {
    font-size: 20px;
  }
}
</style>
